import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { IoArrowBack } from "react-icons/io5";
import { getEvent } from "../../../apis/admin";
import { getEventId } from "../../../apis/user";
import { Event } from "../../../types/event";
import { HashLoader } from "react-spinners";
import Navbar from "../../../components/Navbar/Navbar";
import Footer from "../../../components/Footer/Footer";
import styles from "./EventDashboard.module.css";

interface NavigationItem {
    name: string;
    path: string;
}

const navigationItems: NavigationItem[] = [
    { name: "Overview", path: "" },
    { name: "Settings", path: "Settings" }
];

const EventDashboard: React.FC = () => {
    const { eventName } = useParams<{ eventName: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    
    const [event, setEvent] = useState<Event | null>(null);
    const [eventId, setEventId] = useState<string | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [eventNotFound, setEventNotFound] = useState(false);

    useEffect(() => {
        if (!eventName) {
            setError("Event name is required");
            setIsLoading(false);
            return;
        }

        // First, get the event ID from the event name
        getEventId(eventName, setEventId, setEventNotFound);
    }, [eventName]);

    useEffect(() => {
        if (eventId) {
            // Once we have the event ID, fetch the event details
            getEvent(eventId, setEvent, setIsLoading);
        } else if (eventNotFound) {
            setError("Event not found");
            setIsLoading(false);
        }
    }, [eventId, eventNotFound]);

    const refreshEvent = () => {
        if (eventName) {
            setIsLoading(true);
            setError(null);
            setEventNotFound(false);
            getEventId(eventName, setEventId, setEventNotFound);
        }
    };

    const handleBackClick = () => {
        navigate("/events");
    };

    const handleNavigationClick = (item: NavigationItem) => {
        if (item.path === "") {
            navigate(`/events/${eventName}`);
        } else {
            navigate(`/events/${eventName}/${item.path}`);
        }
    };

    const getCurrentActiveItem = (): string => {
        const pathSegments = location.pathname.split('/');
        const lastSegment = pathSegments[pathSegments.length - 1];

        // If we're at the base dashboard route, Overview is active
        if (lastSegment === eventName) {
            return "";
        }

        return lastSegment;
    };

    if (isLoading) {
        return (
            <div className={styles.container}>
                <Navbar />
                <div className={styles.content}>
                    <div className={styles.loader}>
                        <HashLoader size={50} color="#f5f5f5" />
                        <p>Loading event details...</p>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    if (error || !event) {
        return (
            <div className={styles.container}>
                <Navbar />
                <div className={styles.content}>
                    <div className={styles.error}>
                        <h2>Event Not Found</h2>
                        <p>{error || "The requested event could not be found."}</p>
                        <button 
                            className={styles.backButton}
                            onClick={handleBackClick}
                        >
                            <IoArrowBack size={16} />
                            Back to Events
                        </button>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    const activeItem = getCurrentActiveItem();

    return (
        <div className={styles.container}>
            <Navbar />
            <div className={styles.content}>
                <div className={styles.header}>
                    <div className={styles.headerTop}>
                        <button 
                            className={styles.backButton}
                            onClick={handleBackClick}
                        >
                            <IoArrowBack size={16} />
                            Back
                        </button>
                        <h1 className={styles.eventName}>{event.name}</h1>
                    </div>
                    
                    <nav className={styles.navigation}>
                        <ul className={styles.navList}>
                            {navigationItems.map((item, index) => (
                                <React.Fragment key={item.name}>
                                    <li 
                                        className={`${styles.navItem} ${
                                            activeItem === item.path ? styles.active : ''
                                        }`}
                                        onClick={() => handleNavigationClick(item)}
                                    >
                                        {item.name}
                                    </li>
                                    {index < navigationItems.length - 1 && (
                                        <li className={styles.separator}>|</li>
                                    )}
                                </React.Fragment>
                            ))}
                        </ul>
                    </nav>
                </div>

                <div className={styles.pageContent}>
                    <Outlet context={{ event, refreshEvent }} />
                </div>
            </div>
            <Footer />
        </div>
    );
};

export default EventDashboard;
