import React, { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { updateEvent } from "../../../../apis/admin";
import { Event, UpdateEventRequest } from "../../../../types/event";
import toast from "react-hot-toast";
import Input from "../../ListEvents/components/ui/Input/Input";
import Button from "../../ListEvents/components/ui/Button/Button";
import Toggle from "../../ListEvents/components/ui/Toggle/Toggle";
import styles from "./EventSettings.module.css";

interface EventContextType {
    event: Event;
    refreshEvent: () => void;
}

const EventSettings: React.FC = () => {
    const { event, refreshEvent } = useOutletContext<EventContextType>();
    
    const [formData, setFormData] = useState({
        name: "",
        public_voting: false,
        resubmission_allowed: false,
        is_admin_controlled: false,
        presentation_time_limit: 0,
        voting_time_limit: 0,
    });
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    useEffect(() => {
        if (event) {
            setFormData({
                name: event.name || "",
                public_voting: event.public_voting || false,
                resubmission_allowed: event.resubmission_allowed || false,
                is_admin_controlled: event.is_admin_controlled || false,
                presentation_time_limit: event.presentation_time_limit || 0,
                voting_time_limit: event.voting_time_limit || 0,
            });
        }
    }, [event]);

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.name.trim()) {
            newErrors.name = "Event name is required";
        }

        if (formData.presentation_time_limit < 0) {
            newErrors.presentation_time_limit = "Presentation time limit cannot be negative";
        }

        if (formData.voting_time_limit < 0) {
            newErrors.voting_time_limit = "Voting time limit cannot be negative";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const getDiff = (): UpdateEventRequest => {
        if (!event) return {};

        const diff: UpdateEventRequest = {};

        if (formData.name !== event.name) {
            diff.name = formData.name;
        }
        if (formData.public_voting !== event.public_voting) {
            diff.public_voting = formData.public_voting;
        }
        if (formData.resubmission_allowed !== event.resubmission_allowed) {
            diff.resubmission_allowed = formData.resubmission_allowed;
        }
        if (formData.is_admin_controlled !== event.is_admin_controlled) {
            diff.is_admin_controlled = formData.is_admin_controlled;
        }
        if (formData.presentation_time_limit !== event.presentation_time_limit) {
            diff.presentation_time_limit = formData.presentation_time_limit;
        }
        if (formData.voting_time_limit !== event.voting_time_limit) {
            diff.voting_time_limit = formData.voting_time_limit;
        }

        return diff;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm() || !event) {
            return;
        }

        const diff = getDiff();

        // If no changes, show a message
        if (Object.keys(diff).length === 0) {
            toast.success("No changes to save", {
                id: "no-changes",
            });
            return;
        }

        updateEvent(
            event.id,
            diff,
            setIsLoading,
            (updatedEvent) => {
                refreshEvent();
                toast.success("Event settings updated successfully", {
                    id: "update-settings-success",
                });
            }
        );
    };

    const handleReset = () => {
        if (event) {
            setFormData({
                name: event.name || "",
                public_voting: event.public_voting || false,
                resubmission_allowed: event.resubmission_allowed || false,
                is_admin_controlled: event.is_admin_controlled || false,
                presentation_time_limit: event.presentation_time_limit || 0,
                voting_time_limit: event.voting_time_limit || 0,
            });
            setErrors({});
        }
    };

    if (!event) {
        return (
            <div className={styles.error}>
                <p>Event data not available. Please try again.</p>
                <Button onClick={refreshEvent}>
                    Retry
                </Button>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <h2>Event Settings</h2>
                <p>Configure your event settings and preferences</p>
            </div>

            <form onSubmit={handleSubmit} className={styles.form}>
                <div className={styles.section}>
                    <h3>Basic Information</h3>
                    <Input
                        label="Event Name"
                        value={formData.name}
                        onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
                        placeholder="Enter event name"
                        required
                        error={errors.name}
                        disabled={isLoading}
                    />
                </div>

                <div className={styles.section}>
                    <h3>Time Limits</h3>
                    <div className={styles.row}>
                        <Input
                            label="Presentation Time Limit (minutes)"
                            value={formData.presentation_time_limit}
                            onChange={(value) => setFormData(prev => ({ 
                                ...prev, 
                                presentation_time_limit: parseInt(value) || 0 
                            }))}
                            type="number"
                            placeholder="0"
                            error={errors.presentation_time_limit}
                            disabled={isLoading}
                        />
                        <Input
                            label="Voting Time Limit (minutes)"
                            value={formData.voting_time_limit}
                            onChange={(value) => setFormData(prev => ({ 
                                ...prev, 
                                voting_time_limit: parseInt(value) || 0 
                            }))}
                            type="number"
                            placeholder="0"
                            error={errors.voting_time_limit}
                            disabled={isLoading}
                        />
                    </div>
                </div>

                <div className={styles.section}>
                    <h3>Event Configuration</h3>
                    <div className={styles.toggleGroup}>
                        <Toggle
                            label="Public Voting"
                            checked={formData.public_voting}
                            onChange={(checked) => setFormData(prev => ({ ...prev, public_voting: checked }))}
                            description="Allow public users to vote on this event"
                            disabled={isLoading}
                        />

                        <Toggle
                            label="Resubmission Allowed"
                            checked={formData.resubmission_allowed}
                            onChange={(checked) => setFormData(prev => ({ ...prev, resubmission_allowed: checked }))}
                            description="Allow teams to resubmit their votes"
                            disabled={isLoading}
                        />

                        <Toggle
                            label="Admin Controlled"
                            checked={formData.is_admin_controlled}
                            onChange={(checked) => setFormData(prev => ({ ...prev, is_admin_controlled: checked }))}
                            description="Only admins can control voting and presentation modes"
                            disabled={isLoading}
                        />
                    </div>
                </div>
                
                <div className={styles.buttonGroup}>
                    <Button
                        variant="secondary"
                        onClick={handleReset}
                        disabled={isLoading}
                    >
                        Reset
                    </Button>
                    <Button
                        type="submit"
                        loading={isLoading}
                        disabled={!formData.name.trim()}
                    >
                        Save Changes
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default EventSettings;
