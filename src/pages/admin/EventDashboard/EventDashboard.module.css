.container {
    min-height: calc(100vh - 3.3rem);
    display: flex;
    flex-direction: column;
    background-color: #fbfbf5;
    color: #333132;
}

.content {
    flex: 1;
    padding: 2rem;
    max-width: 1300px;
    margin: 0 auto;
    width: 100%;
}

.header {
    margin-bottom: 2rem;
}

.headerTop {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.backButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    border: 1px solid #666;
    color: #333132;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.backButton:hover {
    background-color: #333132;
    color: #f5f5f5;
    border-color: #333132;
}

.eventName {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #333132;
}

.navigation {
    border-bottom: 1px solid #ddd;
    padding-bottom: 1rem;
}

.navList {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navItem {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    color: #666;
    font-weight: 500;
    font-size: 0.875rem;
}

.navItem:hover {
    background-color: #333132;
    color: #f5f5f5;
}

.navItem.active {
    background-color: #333132;
    color: #f5f5f5;
}

.separator {
    color: #999;
    font-weight: 300;
    user-select: none;
}

.pageContent {
    margin-top: 2rem;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 4rem 2rem;
    text-align: center;
}

.loader p {
    color: #666;
    font-size: 1rem;
}

.error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 4rem 2rem;
    text-align: center;
    background-color: #333132;
    border-radius: 0.5rem;
    margin: 2rem 0;
}

.error h2 {
    color: #f5f5f5;
    font-size: 1.5rem;
    margin: 0;
}

.error p {
    color: #f5f5f5;
    font-size: 1rem;
    margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 1rem;
    }
    
    .headerTop {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .eventName {
        font-size: 1.5rem;
    }
    
    .navList {
        flex-wrap: wrap;
    }
    
    .navItem {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}
