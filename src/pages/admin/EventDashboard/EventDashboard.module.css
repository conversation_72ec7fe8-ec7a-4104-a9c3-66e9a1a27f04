.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #1a1a1a;
    color: #f5f5f5;
}

.content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.header {
    margin-bottom: 2rem;
}

.headerTop {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.backButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    border: 1px solid #333;
    color: #f5f5f5;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.backButton:hover {
    background-color: #333;
    border-color: #555;
}

.eventName {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    color: #f5f5f5;
}

.navigation {
    border-bottom: 1px solid #333;
    padding-bottom: 1rem;
}

.navList {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navItem {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: #ccc;
    font-weight: 500;
}

.navItem:hover {
    background-color: #333;
    color: #f5f5f5;
}

.navItem.active {
    background-color: #4a90e2;
    color: #fff;
}

.separator {
    color: #666;
    font-weight: 300;
    user-select: none;
}

.pageContent {
    margin-top: 2rem;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 4rem 2rem;
    text-align: center;
}

.loader p {
    color: #ccc;
    font-size: 1rem;
}

.error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 4rem 2rem;
    text-align: center;
}

.error h2 {
    color: #f5f5f5;
    font-size: 1.5rem;
    margin: 0;
}

.error p {
    color: #ccc;
    font-size: 1rem;
    margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 1rem;
    }
    
    .headerTop {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .eventName {
        font-size: 1.5rem;
    }
    
    .navList {
        flex-wrap: wrap;
    }
    
    .navItem {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}
