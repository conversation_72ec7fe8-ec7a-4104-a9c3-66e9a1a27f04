.form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.buttonGroup {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    text-align: center;
}

.loader p {
    color: #f5f5f5;
    margin: 0;
}
