import React, { useState } from "react";
import Modal from "../../../../../../../components/Modal/Modal";
import Input from "../../ui/Input/Input";
import Button from "../../ui/Button/Button";
import { createEvent } from "../../../../../../../apis/admin";
import { Event } from "../../../../../../../types/event";
import styles from "./CreateEventModal.module.css";

interface CreateEventModalProps {
    isOpen: boolean;
    onClose: () => void;
    onEventCreated: (event: Event) => void;
    refreshEventsList: () => void;
}

const CreateEventModal: React.FC<CreateEventModalProps> = ({
    isOpen,
    onClose,
    onEventCreated,
    refreshEventsList,
}) => {
    const [eventName, setEventName] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState("");

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!eventName.trim()) {
            setError("Event name is required");
            return;
        }

        setError("");
        console.log("Creating event with name:", eventName.trim());
        createEvent(
            { name: eventName.trim() },
            setIsLoading,
            (event) => {
                console.log("Event created successfully:", event);
                onEventCreated(event);
                handleClose();
            },
            refreshEventsList
        );
    };

    const handleClose = () => {
        setEventName("");
        setError("");
        setIsLoading(false);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <Modal title="Create New Event" onClose={handleClose}>
            <form onSubmit={handleSubmit} className={styles.form}>
                <Input
                    label="Event Name"
                    value={eventName}
                    onChange={setEventName}
                    placeholder="Enter event name"
                    required
                    error={error}
                    disabled={isLoading}
                />
                
                <div className={styles.buttonGroup}>
                    <Button
                        variant="secondary"
                        onClick={handleClose}
                        disabled={isLoading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={isLoading}
                        disabled={!eventName.trim()}
                    >
                        Create Event
                    </Button>
                </div>
            </form>
        </Modal>
    );
};

export default CreateEventModal;
