.content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
}

.warningIcon {
    display: flex;
    justify-content: center;
    align-items: center;
}

.message {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f5f5f5;
    margin: 0;
}

.description {
    font-size: 0.875rem;
    color: #ccc;
    margin: 0;
    line-height: 1.5;
}

.warning {
    font-size: 0.875rem;
    color: #ff6b6b;
    margin: 0;
    font-weight: 500;
}

.buttonGroup {
    display: flex;
    gap: 1rem;
    justify-content: center;
    width: 100%;
}
