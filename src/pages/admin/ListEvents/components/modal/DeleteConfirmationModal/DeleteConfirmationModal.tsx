import React, { useState } from "react";
import Modal from "../../../../../../../components/Modal/Modal";
import Button from "../../ui/Button/Button";
import { deleteEvent } from "../../../../../../../apis/admin";
import { Event } from "../../../../../../../types/event";
import { GoAlertFill } from "react-icons/go";
import styles from "./DeleteConfirmationModal.module.css";

interface DeleteConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    event: Event | null;
    onEventDeleted: (eventId: string) => void;
    refreshEventsList: () => void;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
    isOpen,
    onClose,
    event,
    onEventDeleted,
    refreshEventsList,
}) => {
    const [isLoading, setIsLoading] = useState(false);

    const handleDelete = () => {
        if (!event) return;

        deleteEvent(
            event.id,
            setIsLoading,
            () => {
                onEventDeleted(event.id);
                handleClose();
            },
            refreshEventsList
        );
    };

    const handleClose = () => {
        setIsLoading(false);
        onClose();
    };

    if (!isOpen || !event) return null;

    return (
        <Modal title="Delete Event" onClose={handleClose}>
            <div className={styles.content}>
                <div className={styles.warningIcon}>
                    <GoAlertFill size={48} color="#ff6b6b" />
                </div>
                
                <div className={styles.message}>
                    <h3 className={styles.title}>Are you sure?</h3>
                    <p className={styles.description}>
                        You are about to delete the event <strong>"{event.name}"</strong>. 
                        This action cannot be undone and will permanently remove all associated data.
                    </p>
                    {event.team_count && event.team_count > 0 && (
                        <p className={styles.warning}>
                            This event has {event.team_count} team{event.team_count > 1 ? 's' : ''} associated with it.
                        </p>
                    )}
                </div>

                <div className={styles.buttonGroup}>
                    <Button
                        variant="secondary"
                        onClick={handleClose}
                        disabled={isLoading}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleDelete}
                        loading={isLoading}
                    >
                        Delete Event
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default DeleteConfirmationModal;
