import React from "react";
import { BeatLoader } from "react-spinners";
import styles from "./Button.module.css";

interface ButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    type?: "button" | "submit" | "reset";
    variant?: "primary" | "secondary" | "danger";
    size?: "small" | "medium" | "large";
    disabled?: boolean;
    loading?: boolean;
    fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
    children,
    onClick,
    type = "button",
    variant = "primary",
    size = "medium",
    disabled = false,
    loading = false,
    fullWidth = false,
}) => {
    const className = `${styles.button} ${styles[variant]} ${styles[size]} ${
        fullWidth ? styles.fullWidth : ""
    }`;

    return (
        <button
            type={type}
            onClick={onClick}
            disabled={disabled || loading}
            className={className}
        >
            {loading ? (
                <BeatLoader size={8} color="currentColor" />
            ) : (
                children
            )}
        </button>
    );
};

export default But<PERSON>;
