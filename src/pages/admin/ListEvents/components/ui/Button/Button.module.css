.button {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* Variants */
.primary {
    background-color: #f5f5f5;
    color: #333132;
}

.primary:hover:not(:disabled) {
    background-color: #fbfbf5;
}

.secondary {
    background-color: transparent;
    color: #f5f5f5;
    border: 1px solid #f5f5f5;
}

.secondary:hover:not(:disabled) {
    background-color: #f5f5f5;
    color: #333132;
}

.danger {
    background-color: #ff6b6b;
    color: white;
}

.danger:hover:not(:disabled) {
    background-color: #ff5252;
}

/* Sizes */
.small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.medium {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Full width */
.fullWidth {
    width: 100%;
}
