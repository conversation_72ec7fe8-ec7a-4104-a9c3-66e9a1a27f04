import React from "react";
import styles from "./Toggle.module.css";

interface ToggleProps {
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    disabled?: boolean;
    description?: string;
}

const Toggle: React.FC<ToggleProps> = ({
    label,
    checked,
    onChange,
    disabled = false,
    description,
}) => {
    return (
        <div className={styles.toggleContainer}>
            <div className={styles.toggleHeader}>
                <label className={styles.label}>{label}</label>
                <button
                    type="button"
                    className={`${styles.toggle} ${checked ? styles.checked : ""}`}
                    onClick={() => !disabled && onChange(!checked)}
                    disabled={disabled}
                >
                    <span className={styles.toggleSlider} />
                </button>
            </div>
            {description && (
                <p className={styles.description}>{description}</p>
            )}
        </div>
    );
};

export default Toggle;
