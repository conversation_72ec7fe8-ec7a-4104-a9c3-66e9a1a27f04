import React from "react";
import styles from "./Input.module.css";

interface InputProps {
    label: string;
    value: string | number;
    onChange: (value: string) => void;
    type?: "text" | "number";
    placeholder?: string;
    required?: boolean;
    disabled?: boolean;
    error?: string;
}

const Input: React.FC<InputProps> = ({
    label,
    value,
    onChange,
    type = "text",
    placeholder,
    required = false,
    disabled = false,
    error,
}) => {
    return (
        <div className={styles.inputContainer}>
            <label className={styles.label}>
                {label}
                {required && <span className={styles.required}>*</span>}
            </label>
            <input
                type={type}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                required={required}
                disabled={disabled}
                className={`${styles.input} ${error ? styles.error : ""}`}
            />
            {error && <span className={styles.errorText}>{error}</span>}
        </div>
    );
};

export default Input;
