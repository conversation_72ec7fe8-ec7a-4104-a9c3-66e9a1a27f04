import { createBrowserRouter, RouteObject, RouterProvider } from "react-router-dom";
import AdminDashboard from "./pages/admin/dashboard/AdminDashboard";
import IdeaDashboard from "./pages/admin/idea-dashboard/IdeaDashboard";
import UserVoting from "./pages/user-voting/UserVoting";
import NotFound from "./pages/NotFound/NotFound";
import Landing from "./pages/landing/Landing";
import Authentication from "./pages/auth/Authentication";
import ListEvents from "./pages/admin/ListEvents/ListEvents";
import TeamLeaderboard from "./pages/admin/leaderboard/TeamLeaderboard";
import AuthCheck from "./lib/AuthCheck";
import StorageWrapper from "./components/StorageWrapper";

const routes: RouteObject[] = [
    {
        path: "/",
        element: <Landing />,
    },
    {
        path: "/login",
        element: <Authentication />,
    },
    {
        path: "/",
        element: <AuthCheck />,
        children: [
            {
                path: "/events",
                element: <ListEvents />,
            },
            {
                path: "/:eventName/admin/leaderboard",
                element: <TeamLeaderboard />,
            },
            {
                path: "/:eventName/admin/",
                element: <AdminDashboard />,
            },
            {
                path: "/:eventName/admin/idea/:ideaId",
                element: <IdeaDashboard />,
            },
            {
                path: "/",
                element: <StorageWrapper />,
                children: [
                    {
                        path: "/:eventName/voting",
                        element: <UserVoting />,
                    },
                ],
            },
        ],
    },
    {
        path: "*",
        element: <NotFound />,
    },
];

const router = createBrowserRouter(routes);

export const Routes: React.FC = () => {
    return <RouterProvider router={router} />;
};
